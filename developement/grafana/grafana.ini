[server]
# The IP address to bind to, empty will bind to all interfaces
http_addr =

# The http port to use
http_port = 3000

# The public facing domain name used to access grafana from a browser
domain = localhost

# Redirect to correct domain if host header does not match domain
enforce_domain = false

# The full public facing url you use in browser, used for redirects and emails
root_url = %(protocol)s://%(domain)s:%(http_port)s/

# Serve <PERSON> from subpath specified in `root_url` setting. By default it is set to `false` for compatibility reasons.
serve_from_sub_path = false

[database]
# You can configure the database connection here. For development, sqlite3 is fine.
type = sqlite3
path = grafana.db

[session]
# Either "memory", "file", "redis", "mysql", "postgres", default is "file"
provider = file

# Provider config options
provider_config = sessions

[dataproxy]
# This enables data proxy logging, useful for troubleshooting
logging = false

[analytics]
# Server reporting, sends usage counters to stats.grafana.org every 24 hours.
reporting_enabled = false

# Set to false to disable all checks to https://grafana.net
check_for_updates = false

[security]
# default admin user, created on startup
admin_user = admin

# default admin password, can be changed before first start of grafana,  or in profile settings
admin_password = admin123

# used for signing
secret_key = SW2YcwTIb9zpOOhoPsMm

# disable gravatar profile images
disable_gravatar = false

# data source proxy whitelist (ip_or_domain:port separated by spaces)
data_source_proxy_whitelist =

# disable protection against brute force login attempts
disable_brute_force_login_protection = false

[snapshots]
# snapshot sharing options
external_enabled = true
external_snapshot_url = https://snapshots-origin.raintank.io
external_snapshot_name = Publish to snapshot.raintank.io

[dashboards]
# Number dashboard versions to keep (per dashboard). Default: 20, Minimum: 1
versions_to_keep = 20

[users]
# disable user signup / registration
allow_sign_up = false

# Allow non admin users to create organizations
allow_org_create = true

# Set to true to automatically assign new users to the default organization (id 1)
auto_assign_org = true

# Set this value to automatically add new users to the provided organization (if auto_assign_org above is set to true)
auto_assign_org_id = 1

# Default role new users will be automatically assigned (if disabled above is set to true)
auto_assign_org_role = Viewer

# Require email validation before sign up completes
verify_email_enabled = false

[auth]
# Login cookie name
login_cookie_name = grafana_session

# The maximum lifetime (duration) an authenticated user can be inactive before being required to login at next visit. Default is 7 days (7d). This setting should be expressed as a duration, e.g. 5m (5 minutes), 6h (6 hours), 10d (10 days), 2w (2 weeks), 1M (1 month). The lifetime resets at each successful token rotation.
login_maximum_inactive_lifetime_duration =

# The maximum lifetime (duration) an authenticated user can be logged in since login time before being required to login. Default is 30 days (30d). This setting should be expressed as a duration, e.g. 5m (5 minutes), 6h (6 hours), 10d (10 days), 2w (2 weeks), 1M (1 month).
login_maximum_lifetime_duration =

# How often should auth tokens be rotated for authenticated users when being active. The default is each 10 minutes.
token_rotation_interval_minutes = 10

[auth.anonymous]
# enable anonymous access
enabled = false

[log]
# Either "console", "file", "syslog". Default is console and  file
# Use space to separate multiple modes, e.g. "console file"
mode = console file

# Either "debug", "info", "warn", "error", "critical", default is "info"
level = info

# optional settings to set different levels for specific loggers. Ex filters = sqlstore:debug
filters =

[log.console]
# log line format, valid options are text, console and json
format = console

[log.file]
# log line format, valid options are text, console and json
format = text

# This enables automated log rotate(switch of following options), default is true
log_rotate = true

# Max line number of single file, default is 1000000
max_lines = 1000000

# Max size shift of single file, default is 28 means 1 << 28, 256MB
max_size_shift = 28

# Segment log daily, default is true
daily_rotate = true

# Expired days of log file(delete after max days), default is 7
max_days = 7

[alerting]
# Disable alerting engine & UI features
enabled = true

[metrics]
# Disable / Enable internal metrics
enabled = true

# Graphite Publish interval
interval = 10s

[grafana_net]
url = https://grafana.net

[tracing.jaeger]
# Enable by setting the address sending traces to jaeger (ex localhost:6831)
address = localhost:6831

# Tag that will always be included in when creating new spans. ex (tag1:value1,tag2:value2)
always_included_tag = tag1:value1

# Type specifies the type of the sampler: const, probabilistic, rateLimiting, or remote
sampler_type = const

# jaeger samplerconfig param
# for "const" sampler, 0 or 1 for always false/true respectively
# for "probabilistic" sampler, a probability between 0 and 1
# for "rateLimiting" sampler, the number of spans per second
# for "remote" sampler, param is the same as for "probabilistic"
# and indicates the initial sampling rate before the actual one
# is received from the mothership
sampler_param = 1

# sampling_server_url is the URL of a sampling manager providing a sampling strategy.
sampling_server_url =

# Whether or not to use Zipkin propagation
zipkin_propagation = false

# Setting to reporterconfig.queue_size
queue_size = 100

# Setting to reporterconfig.buffer_flush_interval
buffer_flush_interval = 1s
