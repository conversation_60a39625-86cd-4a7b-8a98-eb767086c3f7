{"cells": [{"cell_type": "code", "execution_count": 1, "id": "11892bf2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting tensorflow\n", "  Downloading tensorflow-2.19.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.1 kB)\n", "Collecting absl-py>=1.0.0 (from tensorflow)\n", "  Downloading absl_py-2.3.1-py3-none-any.whl.metadata (3.3 kB)\n", "Collecting astunparse>=1.6.0 (from tensorflow)\n", "  Downloading astunparse-1.6.3-py2.py3-none-any.whl.metadata (4.4 kB)\n", "Collecting flatbuffers>=24.3.25 (from tensorflow)\n", "  Downloading flatbuffers-25.2.10-py2.py3-none-any.whl.metadata (875 bytes)\n", "Collecting gast!=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1 (from tensorflow)\n", "  Downloading gast-0.6.0-py3-none-any.whl.metadata (1.3 kB)\n", "Collecting google-pasta>=0.1.1 (from tensorflow)\n", "  Downloading google_pasta-0.2.0-py3-none-any.whl.metadata (814 bytes)\n", "Collecting libclang>=13.0.0 (from tensorflow)\n", "  Downloading libclang-18.1.1-py2.py3-none-manylinux2010_x86_64.whl.metadata (5.2 kB)\n", "Collecting opt-einsum>=2.3.2 (from tensorflow)\n", "  Downloading opt_einsum-3.4.0-py3-none-any.whl.metadata (6.3 kB)\n", "Requirement already satisfied: packaging in /home/<USER>/anaconda3/lib/python3.11/site-packages (from tensorflow) (23.1)\n", "Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<6.0.0dev,>=3.20.3 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from tensorflow) (3.20.3)\n", "Requirement already satisfied: requests<3,>=2.21.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from tensorflow) (2.31.0)\n", "Requirement already satisfied: setuptools in /home/<USER>/anaconda3/lib/python3.11/site-packages (from tensorflow) (68.2.2)\n", "Requirement already satisfied: six>=1.12.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from tensorflow) (1.16.0)\n", "Collecting termcolor>=1.1.0 (from tensorflow)\n", "  Downloading termcolor-3.1.0-py3-none-any.whl.metadata (6.4 kB)\n", "Requirement already satisfied: typing-extensions>=3.6.6 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from tensorflow) (4.9.0)\n", "Requirement already satisfied: wrapt>=1.11.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from tensorflow) (1.14.1)\n", "Collecting grpcio<2.0,>=1.24.3 (from tensorflow)\n", "  Downloading grpcio-1.73.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.8 kB)\n", "Collecting tensorboard~=2.19.0 (from tensorflow)\n", "  Downloading tensorboard-2.19.0-py3-none-any.whl.metadata (1.8 kB)\n", "Collecting keras>=3.5.0 (from tensorflow)\n", "  Downloading keras-3.10.0-py3-none-any.whl.metadata (6.0 kB)\n", "Requirement already satisfied: numpy<2.2.0,>=1.26.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from tensorflow) (1.26.4)\n", "Collecting h5py>=3.11.0 (from tensorflow)\n", "  Downloading h5py-3.14.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.7 kB)\n", "Collecting ml-dtypes<1.0.0,>=0.5.1 (from tensorflow)\n", "  Downloading ml_dtypes-0.5.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (21 kB)\n", "Collecting tensorflow-io-gcs-filesystem>=0.23.1 (from tensorflow)\n", "  Downloading tensorflow_io_gcs_filesystem-0.37.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (14 kB)\n", "Requirement already satisfied: wheel<1.0,>=0.23.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from astunparse>=1.6.0->tensorflow) (0.41.2)\n", "Requirement already satisfied: rich in /home/<USER>/anaconda3/lib/python3.11/site-packages (from keras>=3.5.0->tensorflow) (13.3.5)\n", "Collecting namex (from keras>=3.5.0->tensorflow)\n", "  Downloading namex-0.1.0-py3-none-any.whl.metadata (322 bytes)\n", "Collecting optree (from keras>=3.5.0->tensorflow)\n", "  Downloading optree-0.16.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (30 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow) (2.0.4)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow) (2024.2.2)\n", "Requirement already satisfied: markdown>=2.6.8 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from tensorboard~=2.19.0->tensorflow) (3.4.1)\n", "Collecting tensorboard-data-server<0.8.0,>=0.7.0 (from tensorboard~=2.19.0->tensorflow)\n", "  Downloading tensorboard_data_server-0.7.2-py3-none-manylinux_2_31_x86_64.whl.metadata (1.1 kB)\n", "Requirement already satisfied: werkzeug>=1.0.1 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from tensorboard~=2.19.0->tensorflow) (2.2.3)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from werkzeug>=1.0.1->tensorboard~=2.19.0->tensorflow) (2.1.3)\n", "Requirement already satisfied: markdown-it-py<3.0.0,>=2.2.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from rich->keras>=3.5.0->tensorflow) (2.2.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from rich->keras>=3.5.0->tensorflow) (2.15.1)\n", "Requirement already satisfied: mdurl~=0.1 in /home/<USER>/anaconda3/lib/python3.11/site-packages (from markdown-it-py<3.0.0,>=2.2.0->rich->keras>=3.5.0->tensorflow) (0.1.0)\n", "Downloading tensorflow-2.19.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (644.9 MB)\n", "\u001b[2K   \u001b[91m━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.2/644.9 MB\u001b[0m \u001b[31m11.7 MB/s\u001b[0m eta \u001b[36m0:00:52\u001b[0m\n", "\u001b[?25h\u001b[31mERROR: Operation cancelled by user\u001b[0m\u001b[31m\n", "\u001b[0m^C\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/anaconda3/bin/pip\", line 11, in <module>\n", "    sys.exit(main())\n", "             ^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/cli/main.py\", line 79, in main\n", "    return command.main(cmd_args)\n", "           ^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/cli/base_command.py\", line 101, in main\n", "    return self._main(args)\n", "           ^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/cli/base_command.py\", line 236, in _main\n", "    self.handle_pip_version_check(options)\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/cli/req_command.py\", line 177, in handle_pip_version_check\n", "    session = self._build_session(\n", "              ^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/cli/req_command.py\", line 122, in _build_session\n", "    session = PipSession(\n", "              ^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/network/session.py\", line 342, in __init__\n", "    self.headers[\"User-Agent\"] = user_agent()\n", "                                 ^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/network/session.py\", line 175, in user_agent\n", "    setuptools_dist = get_default_environment().get_distribution(\"setuptools\")\n", "                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/metadata/importlib/_envs.py\", line 189, in get_distribution\n", "    return next(matches, None)\n", "           ^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/metadata/importlib/_envs.py\", line 184, in <genexpr>\n", "    matches = (\n", "              ^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/metadata/base.py\", line 626, in iter_all_distributions\n", "    for dist in self._iter_distributions():\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/metadata/importlib/_envs.py\", line 176, in _iter_distributions\n", "    yield from finder.find(location)\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/metadata/importlib/_envs.py\", line 79, in find\n", "    for dist, info_location in self._find_impl(location):\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/metadata/importlib/_envs.py\", line 64, in _find_impl\n", "    raw_name = get_dist_name(dist)\n", "               ^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/site-packages/pip/_internal/metadata/importlib/_compat.py\", line 52, in get_dist_name\n", "    name = cast(Any, dist).name\n", "           ^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/importlib/metadata/__init__.py\", line 622, in name\n", "    return self.metadata['Name']\n", "           ^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/importlib/metadata/__init__.py\", line 610, in metadata\n", "    self.read_text('METADATA')\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/importlib/metadata/__init__.py\", line 938, in read_text\n", "    return self._path.joinpath(filename).read_text(encoding='utf-8')\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/lib/python3.11/pathlib.py\", line 1059, in read_text\n", "    return f.read()\n", "           ^^^^^^^^\n", "KeyboardInterrupt\n"]}], "source": ["!pip install tensorflow"]}, {"cell_type": "code", "execution_count": null, "id": "685cc9dc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}