global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # Add more scrape configs here for your applications
  # Example for a web application:
  # - job_name: 'my-app'
  #   static_configs:
  #     - targets: ['my-app:8080']
  #   metrics_path: '/metrics'
  #   scrape_interval: 5s
