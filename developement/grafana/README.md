# Grafana Dashboard Setup

This directory contains a complete Grafana setup with Docker Compose, including Prometheus for metrics collection and Node Exporter for system monitoring.

## Quick Start

1. **Start the services:**
   ```bash
   cd developement/grafana
   docker-compose up -d
   ```

2. **Access Grafana:**
   - URL: http://localhost:3000
   - Username: `admin`
   - Password: `admin123`

3. **Access Prometheus (optional):**
   - URL: http://localhost:9090

## Services Included

- **Grafana** (port 3000): Main dashboard interface
- **Prometheus** (port 9090): Metrics collection and storage
- **Node Exporter** (port 9100): System metrics exporter

## Pre-configured Features

- **Data Source**: Prometheus is automatically configured as the default data source
- **Sample Dashboard**: "System Overview" dashboard with CPU and Memory usage
- **Plugins**: Clock panel, Simple JSON datasource, and World map panel

## File Structure

```
grafana/
├── docker-compose.yml          # Docker services configuration
├── grafana.ini                 # Grafana configuration
├── prometheus.yml              # Prometheus configuration
├── provisioning/
│   ├── datasources/
│   │   └── prometheus.yml      # Auto-configured Prometheus datasource
│   └── dashboards/
│       └── dashboard.yml       # Dashboard provider configuration
├── dashboards/
│   └── system-overview.json    # Sample system monitoring dashboard
└── README.md                   # This file
```

## Common Commands

### Start services
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### View logs
```bash
docker-compose logs -f grafana
docker-compose logs -f prometheus
```

### Restart a specific service
```bash
docker-compose restart grafana
```

## Customization

### Adding New Data Sources
1. Create YAML files in `provisioning/datasources/`
2. Restart Grafana: `docker-compose restart grafana`

### Adding New Dashboards
1. Export dashboard JSON from Grafana UI
2. Place JSON files in `dashboards/` directory
3. Dashboards will be automatically loaded

### Modifying Configuration
- Edit `grafana.ini` for Grafana settings
- Edit `prometheus.yml` for Prometheus configuration
- Restart services after changes

## Security Notes

- Default admin password is `admin123` - change this in production!
- User signup is disabled by default
- Consider using environment variables for sensitive data in production

## Troubleshooting

### Port Conflicts
If ports 3000, 9090, or 9100 are already in use, modify the port mappings in `docker-compose.yml`:
```yaml
ports:
  - "3001:3000"  # Change 3000 to 3001
```

### Permission Issues
If you encounter permission issues with volumes:
```bash
sudo chown -R 472:472 ./grafana-data
```

### Data Persistence
Data is stored in Docker volumes:
- `grafana-storage`: Grafana data and dashboards
- `prometheus-storage`: Prometheus metrics data

To backup data:
```bash
docker run --rm -v grafana_grafana-storage:/data -v $(pwd):/backup alpine tar czf /backup/grafana-backup.tar.gz -C /data .
```

## Next Steps

1. **Add More Metrics**: Configure your applications to expose metrics
2. **Create Custom Dashboards**: Use the Grafana UI to build dashboards
3. **Set Up Alerts**: Configure alerting rules in Grafana
4. **Add More Data Sources**: Connect databases, logs, etc.

## Useful Links

- [Grafana Documentation](https://grafana.com/docs/)
- [Prometheus Documentation](https://prometheus.io/docs/)
- [Dashboard Gallery](https://grafana.com/grafana/dashboards/)
